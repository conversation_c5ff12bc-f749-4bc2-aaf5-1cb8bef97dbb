-- =============================================
-- Oracle版本 - 创建原始视图脚本 (补充)
-- 功能：创建仓库管理系统的原始视图 (Oracle版本)
-- 用途：从SQL Server NEPISQL.sql转换的原始3个视图
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中的3个SQL Server视图转换为Oracle语法
-- 包含：LOTxID、LOTxLOC、IDxLOC等原始库存汇总视图
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 创建原始库存汇总视图 (从SQL Server转换)
-- =============================================

-- LOTxID视图 (从SQL Server LOTxID视图转换)
-- 功能：按LOT和ID汇总库存数量
CREATE OR REPLACE VIEW LOTxID (
    Lot,
    Id,
    Qty
) AS
SELECT 
    LOTxLOCxID.Lot,
    LOTxLOCxID.Id,
    SUM(LOTxLOCxID.Qty) AS Qty
FROM LOTxLOCxID
GROUP BY 
    LOTxLOCxID.Lot,
    LOTxLOCxID.Id
HAVING SUM(LOTxLOCxID.Qty) > 0;

-- 授予权限
GRANT SELECT ON LOTxID TO nsql;
GRANT INSERT ON LOTxID TO nsql;
GRANT UPDATE ON LOTxID TO nsql;
GRANT DELETE ON LOTxID TO nsql;

PROMPT '>>> 已创建视图 LOTxID (LOT和ID库存汇总视图)';

-- LOTxLOC视图 (从SQL Server LOTxLOC视图转换)
-- 功能：按LOT、位置、存储商和SKU汇总库存数量
CREATE OR REPLACE VIEW LOTxLOC (
    Lot,
    Loc,
    StorerKey,
    Sku,
    Qty
) AS
SELECT 
    LOTxLOCxID.Lot,
    LOTxLOCxID.Loc,
    LOTxLOCxID.StorerKey,
    LOTxLOCxID.Sku,
    SUM(LOTxLOCxID.Qty) AS Qty
FROM LOTxLOCxID
GROUP BY 
    LOTxLOCxID.Lot,
    LOTxLOCxID.Loc,
    LOTxLOCxID.StorerKey,
    LOTxLOCxID.Sku
HAVING SUM(LOTxLOCxID.Qty) > 0;

-- 授予权限
GRANT SELECT ON LOTxLOC TO nsql;
GRANT INSERT ON LOTxLOC TO nsql;
GRANT UPDATE ON LOTxLOC TO nsql;
GRANT DELETE ON LOTxLOC TO nsql;

PROMPT '>>> 已创建视图 LOTxLOC (LOT和位置库存汇总视图)';

-- IDxLOC视图 (从SQL Server IDxLOC视图转换)
-- 功能：按位置和ID汇总库存数量
CREATE OR REPLACE VIEW IDxLOC (
    Loc,
    Id,
    Qty
) AS
SELECT 
    LOTxLOCxID.Loc,
    LOTxLOCxID.Id,
    SUM(LOTxLOCxID.Qty) AS Qty
FROM LOTxLOCxID
GROUP BY 
    LOTxLOCxID.Loc,
    LOTxLOCxID.Id
HAVING SUM(LOTxLOCxID.Qty) > 0;

-- 授予权限
GRANT SELECT ON IDxLOC TO nsql;
GRANT INSERT ON IDxLOC TO nsql;
GRANT UPDATE ON IDxLOC TO nsql;
GRANT DELETE ON IDxLOC TO nsql;

PROMPT '>>> 已创建视图 IDxLOC (ID和位置库存汇总视图)';

-- =============================================
-- 创建额外的有用视图 (Oracle增强)
-- =============================================

-- SKU库存汇总视图 (Oracle增强)
-- 功能：按存储商和SKU汇总库存数量
CREATE OR REPLACE VIEW V_SKU_INVENTORY AS
SELECT 
    lli.StorerKey,
    lli.Sku,
    s.DESCR AS SkuDescription,
    s.UOM,
    SUM(lli.Qty) AS TotalQty,
    SUM(lli.QtyAllocated) AS TotalAllocated,
    SUM(lli.QtyPicked) AS TotalPicked,
    SUM(lli.Qty - NVL(lli.QtyAllocated, 0) - NVL(lli.QtyPicked, 0)) AS AvailableQty,
    COUNT(DISTINCT lli.Loc) AS LocationCount,
    COUNT(DISTINCT lli.Lot) AS LotCount,
    COUNT(DISTINCT lli.Id) AS IdCount,
    MIN(lli.AddDate) AS FirstReceiveDate,
    MAX(lli.EditDate) AS LastUpdateDate
FROM LOTxLOCxID lli
JOIN SKU s ON lli.StorerKey = s.StorerKey AND lli.Sku = s.Sku
WHERE lli.Qty > 0
GROUP BY 
    lli.StorerKey, 
    lli.Sku, 
    s.DESCR,
    s.UOM;

-- 授予权限
GRANT SELECT ON V_SKU_INVENTORY TO nsql;

PROMPT '>>> 已创建视图 V_SKU_INVENTORY (SKU库存汇总视图)';

-- 位置库存汇总视图 (Oracle增强)
-- 功能：按位置汇总库存数量和SKU种类
CREATE OR REPLACE VIEW V_LOCATION_INVENTORY AS
SELECT 
    lli.Loc,
    l.LocationType,
    l.PutawayZone,
    l.Status AS LocationStatus,
    COUNT(DISTINCT lli.StorerKey || '|' || lli.Sku) AS SKUCount,
    COUNT(DISTINCT lli.Lot) AS LotCount,
    COUNT(DISTINCT lli.Id) AS IdCount,
    SUM(lli.Qty) AS TotalQty,
    SUM(lli.QtyAllocated) AS TotalAllocated,
    SUM(lli.QtyPicked) AS TotalPicked,
    SUM(lli.Qty - NVL(lli.QtyAllocated, 0) - NVL(lli.QtyPicked, 0)) AS AvailableQty,
    CASE 
        WHEN l.MaxQty > 0 THEN ROUND((SUM(lli.Qty) / l.MaxQty) * 100, 2)
        ELSE 0
    END AS QtyUtilization,
    MIN(lli.AddDate) AS FirstReceiveDate,
    MAX(lli.EditDate) AS LastUpdateDate
FROM LOTxLOCxID lli
JOIN LOC l ON lli.Loc = l.Loc
WHERE lli.Qty > 0
GROUP BY 
    lli.Loc,
    l.LocationType,
    l.PutawayZone,
    l.Status,
    l.MaxQty;

-- 授予权限
GRANT SELECT ON V_LOCATION_INVENTORY TO nsql;

PROMPT '>>> 已创建视图 V_LOCATION_INVENTORY (位置库存汇总视图)';

-- LOT库存汇总视图 (Oracle增强)
-- 功能：按LOT汇总库存数量和分布
CREATE OR REPLACE VIEW V_LOT_INVENTORY AS
SELECT 
    lli.Lot,
    lot.LotDate,
    lot.ExpiryDate,
    lot.Status AS LotStatus,
    COUNT(DISTINCT lli.StorerKey || '|' || lli.Sku) AS SKUCount,
    COUNT(DISTINCT lli.Loc) AS LocationCount,
    COUNT(DISTINCT lli.Id) AS IdCount,
    SUM(lli.Qty) AS TotalQty,
    SUM(lli.QtyAllocated) AS TotalAllocated,
    SUM(lli.QtyPicked) AS TotalPicked,
    SUM(lli.Qty - NVL(lli.QtyAllocated, 0) - NVL(lli.QtyPicked, 0)) AS AvailableQty,
    MIN(lli.AddDate) AS FirstReceiveDate,
    MAX(lli.EditDate) AS LastUpdateDate,
    CASE 
        WHEN lot.ExpiryDate IS NOT NULL THEN 
            CASE 
                WHEN lot.ExpiryDate < SYSDATE THEN 'EXPIRED'
                WHEN lot.ExpiryDate < SYSDATE + 30 THEN 'EXPIRING_SOON'
                ELSE 'GOOD'
            END
        ELSE 'NO_EXPIRY'
    END AS ExpiryStatus
FROM LOTxLOCxID lli
LEFT JOIN LOT lot ON lli.Lot = lot.Lot
WHERE lli.Qty > 0
GROUP BY 
    lli.Lot,
    lot.LotDate,
    lot.ExpiryDate,
    lot.Status;

-- 授予权限
GRANT SELECT ON V_LOT_INVENTORY TO nsql;

PROMPT '>>> 已创建视图 V_LOT_INVENTORY (LOT库存汇总视图)';

-- ID库存汇总视图 (Oracle增强)
-- 功能：按ID汇总库存数量和分布
CREATE OR REPLACE VIEW V_ID_INVENTORY AS
SELECT 
    lli.Id,
    id_info.Status AS IdStatus,
    COUNT(DISTINCT lli.StorerKey || '|' || lli.Sku) AS SKUCount,
    COUNT(DISTINCT lli.Loc) AS LocationCount,
    COUNT(DISTINCT lli.Lot) AS LotCount,
    SUM(lli.Qty) AS TotalQty,
    SUM(lli.QtyAllocated) AS TotalAllocated,
    SUM(lli.QtyPicked) AS TotalPicked,
    SUM(lli.Qty - NVL(lli.QtyAllocated, 0) - NVL(lli.QtyPicked, 0)) AS AvailableQty,
    MIN(lli.AddDate) AS FirstReceiveDate,
    MAX(lli.EditDate) AS LastUpdateDate
FROM LOTxLOCxID lli
LEFT JOIN ID id_info ON lli.Id = id_info.Id
WHERE lli.Qty > 0
GROUP BY 
    lli.Id,
    id_info.Status;

-- 授予权限
GRANT SELECT ON V_ID_INVENTORY TO nsql;

PROMPT '>>> 已创建视图 V_ID_INVENTORY (ID库存汇总视图)';

-- 提交事务
COMMIT;

PROMPT '>>> Oracle原始视图脚本执行完成';
PROMPT '>>> 已创建8个库存汇总视图 (3个原始 + 5个增强)';
PROMPT '>>> 功能覆盖：';
PROMPT '>>> - LOT和ID库存汇总 (原始)';
PROMPT '>>> - LOT和位置库存汇总 (原始)';
PROMPT '>>> - ID和位置库存汇总 (原始)';
PROMPT '>>> - SKU库存汇总 (增强)';
PROMPT '>>> - 位置库存汇总 (增强)';
PROMPT '>>> - LOT库存汇总 (增强)';
PROMPT '>>> - ID库存汇总 (增强)';

-- 显示完成信息
PROMPT '=============================================';
PROMPT 'Oracle版本 - 原始视图创建完成！';
PROMPT '总视图数量：8个库存汇总视图';
PROMPT '原始视图：3个 (从SQL Server转换)';
PROMPT '增强视图：5个 (Oracle优化增强)';
PROMPT '功能覆盖：';
PROMPT '- 完整的库存维度汇总';
PROMPT '- 多层次库存分析';
PROMPT '- 库存状态监控';
PROMPT '- 过期库存管理';
PROMPT '- 位置利用率分析';
PROMPT '=============================================';
