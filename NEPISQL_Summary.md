# NEPISQL.SQL 文件分析汇总

## 概述
已成功将 NEPISQL.SQL 文件中的存储过程和触发器分离为两个独立的文件。

## 文件分离结果

### 1. 存储过程文件
- **文件名**: `NEPISQL_StoredProcedures.sql`
- **数量**: 167个存储过程
- **位置**: 原文件第12570行 - 第62629行

### 2. 触发器文件
- **文件名**: `NEPISQL_Triggers.sql`
- **数量**: 108个触发器
- **位置**: 原文件第62630行 - 第79363行

## 存储过程列表（167个）

### 核心系统存储过程
1. nsp_logerror - 错误日志记录
2. nspg_getkey - 主键生成
3. nspLogAlert - 警报日志
4. nspUOMCONV - 单位转换
5. nsp_lotgen - 批次生成
6. nsp_lotlookup - 批次查找
7. nspg_GETSKU - SKU获取
8. nspGetPack - 包装获取
9. nspInventoryHold - 库存冻结
10. nspInventoryHoldResultSet - 库存冻结结果集

### 库存事务处理存储过程
11. nspItrnAddDWBill - 添加存取单据
12. nspItrnAddDepositCheck - 存入检查
13. nspItrnAddDeposit - 存入处理
14. nspItrnAddWithdrawalCheck - 取出检查
15. nspItrnAddWithdrawal - 取出处理
16. nspItrnAddAdjustmentCheck - 调整检查
17. nspItrnAddAdjustment - 调整处理
18. nspItrnAddMove - 移动处理
19. nspItrnAddMoveCheck - 移动检查

### 订单处理存储过程
20. nspCartonization - 装箱处理
21. nspOrderWaveBatchQty - 波次批量数量
22. nspOrderWaveZoneSim - 波次区域模拟
23. nspOrderWaveBatchZoneCube - 波次批量区域体积
24. nspOrderWaveBatchZoneQty - 波次批量区域数量
25. nspOrderWaveBatchCube - 波次批量体积
26. nspOrderWaveDiscrete - 波次离散处理
27. nspOrderProcessingWave - 订单处理波次
28. nspPreAllocateOrderProcessing - 预分配订单处理
29. nspOrderProcessing - 订单处理
30. nsp_orderprocessing_wrapper - 订单处理包装器

### RF（射频）相关存储过程
31. nspRFRC01-03 - RF接收相关
32. nspRFRL01 - RF接收列表
33. nspRFOT02-08 - RF出库相关
34. nspRFOP01-04 - RF操作相关
35. nspRFQC01 - RF质检
36. nspRFRP01-04 - RF报告相关
37. nspRFIQ01-03 - RF库存查询
38. nspRFPA01-02 - RF拣货分配
39. nspRFPH01-03 - RF拣货处理

### 库存管理存储过程
40. nspFIFO - 先进先出
41. nspPTH - 路径跟踪头
42. nspPTD - 路径跟踪明细
43. nspPASTD - 过去标准
44. nspStockMovementSku - SKU库存移动
45. nspStockMovementLot - 批次库存移动
46. nspLOTLOCIDUniqueRow - 批次位置ID唯一行

### 报表和分析存储过程
47. nspStockMovementLotDetail - 批次库存移动明细
48. nspStockMovementSkuDetail - SKU库存移动明细
49. nspPODetailDiscrep - 采购订单明细差异
50. nspReceiptDetailDiscrep - 收货明细差异
51. nspStockHiLoSku - SKU库存高低点
52. nspOrderStatusGraph - 订单状态图表

### 盘点相关存储过程
53. nspCompare_a2b_id - A到B ID比较
54. nspCompare_a2b_lot - A到B批次比较
55. nspCompare_a2b_sku - A到B SKU比较
56. nspCompare_a2b_tag - A到B标签比较
57. nspCompare_inv2a_sku - 库存到A SKU比较
58. nspCompare_inv2a_lot - 库存到A批次比较
59. nspCompare_inv2a_id - 库存到A ID比较
60. nspMissing_tag - 缺失标签
61. nspOutofrange_tag - 超范围标签
62. nsp_post_physical - 过账盘点

### 数据归档存储过程
63. nsp_BUILD_ARCHIVE_TABLE - 构建归档表
64. nsp_BUILD_INSERT - 构建插入
65. nspBuildAlterTableString - 构建修改表字符串
66. nspArchiveCaseManifest - 归档箱单
67. nspArchiveReceipt - 归档收货
68. NspArchiveContainer - 归档容器
69. nspArchivePallet - 归档托盘
70. nspArchivePO - 归档采购订单
71. nspArchiveHAWB - 归档分运单
72. nspArchiveMawb - 归档主运单
73. nspArchiveMBOL - 归档主提单

### 计费相关存储过程
74. nspCalculateRSCharges - 计算RS费用
75. nspCalculateRSChargesWrapper - 计算RS费用包装器
76. nspBillContainer - 容器计费
77. nspBillingRun - 计费运行
78. nspBillingRunWrapper - 计费运行包装器
79. nspBillDocumentMinimumCharge - 单据最小费用
80. nspBillInvoiceMinimumCharge - 发票最小费用
81. nspBillLotMinimumCharge - 批次最小费用
82. nspBillRetrievePendingCharge - 检索待处理费用
83. nspBillSetInvoiceNumbers - 设置发票号码
84. nspBillTaxCharge - 税费计费

### 任务管理存储过程
85. nspTTMCC01 - 任务管理循环盘点
86. nspTTMCO01 - 任务管理容器操作
87. nspTTMGM01 - 任务管理通用移动
88. nspTTMMV01 - 任务管理移动
89. nspTTMPA01 - 任务管理拣货分配
90. nspTTMQC01 - 任务管理质检
91. nspTTMXD01 - 任务管理交叉停靠
92. nspAddSkipTasks - 添加跳过任务
93. nspCheckSkipTasks - 检查跳过任务
94. nspCheckMoveQty - 检查移动数量
95. nspCheckEquipmentProfile - 检查设备配置

### 任务评估存储过程
96. nspTTMEvaluateCCTasks - 评估循环盘点任务
97. nspTTMEvaluateCOTasks - 评估容器操作任务
98. nspTTMEvaluateGMTasks - 评估通用移动任务
99. nspTTMEvaluateMVTasks - 评估移动任务
100. nspTTMEvaluatePATasks - 评估拣货分配任务
101. nspTTMEvaluatePKTasks - 评估拣货任务
102. nspTTMEvaluateRPTasks - 评估补货任务
103. nspTTMEvaluateQCTasks - 评估质检任务
104. nspTTMEvaluateXDTasks - 评估交叉停靠任务

### RF任务相关存储过程
105. nspTMTM01 - 任务管理主程序
106. nspRFTPK01 - RF任务拣货
107. nspRFTRP01 - RF任务补货
108. nspRFTCC01-02 - RF任务循环盘点
109. nspRFTPA01-02 - RF任务拣货分配
110. nspRFTMV01 - RF任务移动
111. nspTMTM03 - 任务管理程序3
112. nspTTMPK01 - 任务管理拣货

### 波次和拣货存储过程
113. nspReleaseWave - 释放波次
114. nsp_addpicklocation - 添加拣货位置
115. nspPendingMoveInUpdate - 待处理移入更新
116. nspCheckDropId - 检查投放ID
117. nspShipDropID - 发货投放ID
118. nspRFSH01 - RF发货

### 标准报表存储过程
119. nspPRstd01-06 - 标准报表1-6
120. nspALstd01-06 - 分配标准1-6
121. nspPR01_07 - 报表1-7
122. nspAL01_07 - 分配1-7
123. nspAL02_02 - 分配2-2
124. nspAL02_06 - 分配2-6

### 系统管理存储过程
125. nspPoll - 轮询
126. nspRIGHTS - 权限
127. nspaltertable - 修改表结构

### 其他归档存储过程
128. nspArchiveShippingOrder - 归档发货订单
129. nspArchiveTransfer - 归档转移
130. nspArchiveInventory - 归档库存
131. nspArchiveAdjustment - 归档调整
132. NspArchiveParameters - 归档参数

### 图表和分析存储过程
133. nspOrderProcessingCasePie - 订单处理箱饼图
134. nspOrderProcCaseGraph - 订单处理箱图表
135. nspOrderProcPickGraph - 订单处理拣货图表
136. nspOrderProcPickPie - 订单处理拣货饼图
137. nspOrderProcAktiv - 订单处理活动

### 数据填充存储过程
138. nsp_fill_StorerkeySkuLot_id_a - 填充存储商SKU批次ID
139. nsp_fill_StorerkeySku_lot_a - 填充存储商SKU批次
140. nsp_fill_lot_StorerkeySku_a - 填充批次存储商SKU

### 比较功能存储过程
141-167. 其他比较、验证和处理相关存储过程

## 触发器列表（108个）

### 库存事务触发器
1. ntrItrnAdd - 库存事务添加触发器

### 订单相关触发器
2. ntrOrderDetailAdd - 订单明细添加
3. ntrOrderDetailDelete - 订单明细删除
4. ntrOrderDetailUpdate - 订单明细更新
5. ntrOrderHeaderAdd - 订单头添加
6. ntrOrderHeaderDelete - 订单头删除
7. ntrOrderHeaderUpdate - 订单头更新

### 拣货相关触发器
8. ntrPickHeaderUpdate - 拣货头更新
9. ntrPickHeaderDelete - 拣货头删除
10. ntrPickDetailAdd - 拣货明细添加
11. ntrPickDetailUpdate - 拣货明细更新
12. ntrPickDetailDelete - 拣货明细删除

### 收货相关触发器
13. ntrReceiptHeaderAdd - 收货头添加
14. ntrReceiptHeaderDelete - 收货头删除
15. ntrReceiptHeaderUpdate - 收货头更新
16. ntrReceiptDetailAdd - 收货明细添加
17. ntrReceiptDetailDelete - 收货明细删除
18. ntrReceiptDetailUpdate - 收货明细更新

### 采购订单触发器
19. ntrPOHeaderAdd - 采购订单头添加
20. ntrPOHeaderDelete - 采购订单头删除
21. ntrPOHeaderUpdate - 采购订单头更新
22. ntrPODetailAdd - 采购订单明细添加
23. ntrPODetailDelete - 采购订单明细删除
24. ntrPODetailUpdate - 采购订单明细更新

### 调整相关触发器
25. ntrAdjustmentHeaderAdd - 调整头添加
26. ntrAdjustmentHeaderDelete - 调整头删除
27. ntrAdjustmentHeaderUpdate - 调整头更新
28. ntrAdjustmentDetailAdd - 调整明细添加
29. ntrAdjustmentDetailUpdate - 调整明细更新
30. ntrAdjustmentDetailDelete - 调整明细删除

### 容器相关触发器
31. ntrContainerHeaderAdd - 容器头添加
32. ntrContainerHeaderUpdate - 容器头更新
33. ntrContainerHeaderDelete - 容器头删除
34. ntrContainerDetailAdd - 容器明细添加
35. ntrContainerDetailUpdate - 容器明细更新
36. ntrContainerDetailDelete - 容器明细删除

### 箱单相关触发器
37. ntrCaseManifestAdd - 箱单添加
38. ntrCaseManifestDelete - 箱单删除
39. ntrCaseManifestUpdate - 箱单更新

### 托盘相关触发器
40. ntrPalletHeaderAdd - 托盘头添加
41. ntrPalletHeaderDelete - 托盘头删除
42. ntrPalletHeaderUpdate - 托盘头更新
43. ntrPalletDetailAdd - 托盘明细添加
44. ntrPalletDetailDelete - 托盘明细删除
45. ntrPalletDetailUpdate - 托盘明细更新

### 转移相关触发器
46. ntrTransferHeaderAdd - 转移头添加
47. ntrTransferHeaderDelete - 转移头删除
48. ntrTransferHeaderUpdate - 转移头更新
49. ntrTransferDetailAdd - 转移明细添加
50. ntrTransferDetailUpdate - 转移明细更新
51. ntrTransferDetailDelete - 转移明细删除

### 航空运单触发器
52. ntrMasterAirWayBillAdd - 主运单添加
53. ntrMasterAirWayBillUpdate - 主运单更新
54. ntrMasterAirWayBillDelete - 主运单删除
55. ntrMasterAirWayBillDetailAdd - 主运单明细添加
56. ntrMasterAirWayBillDetailUpd - 主运单明细更新
57. ntrMasterAirWayBillDetailDel - 主运单明细删除
58. ntrHouseAirWayBillAdd - 分运单添加
59. ntrHouseAirWayBillUpdate - 分运单更新
60. ntrHouseAirWayBillDelete - 分运单删除
61. ntrHouseAirWayBillDetailAdd - 分运单明细添加
62. ntrHouseAirWayBillDetailUpd - 分运单明细更新
63. ntrHouseAirWayBillDetailDel - 分运单明细删除

### 提单相关触发器
64. ntrMbolHeaderAdd - 主提单头添加
65. ntrMBOLHeaderUpdate - 主提单头更新
66. ntrMbolHeaderDelete - 主提单头删除
67. ntrMBOLDetailAdd - 主提单明细添加
68. ntrMBOLDetailDelete - 主提单明细删除
69. ntrMBOLDetailUpdate - 主提单明细更新

### 其他业务触发器
70. ntrSkuXLocUpdate - SKU位置更新
71. ntrXdockHeaderAdd - 交叉停靠头添加
72. ntrXDockHeaderDelete - 交叉停靠头删除
73. ntrXdockHeaderUpdate - 交叉停靠头更新
74. ntrXDockDetailAdd - 交叉停靠明细添加
75. ntrXDockDetailDelete - 交叉停靠明细删除
76. ntrXDockDetailUpdate - 交叉停靠明细更新

### 预分配触发器
77. ntrPreAllocatePickDetailAdd - 预分配拣货明细添加
78. ntrPreAllocatePickDetailUpdate - 预分配拣货明细更新
79. ntrPreAllocatePickDetailDelete - 预分配拣货明细删除

### 基础数据触发器
80. ntrLocUpdate - 位置更新
81. ntrPackAdd - 包装添加
82. ntrPackUpdate - 包装更新
83. ntrPackDelete - 包装删除

### 盘点相关触发器
84. ntrCCAdd - 循环盘点添加
85. ntrCCUpdate - 循环盘点更新
86. ntrCCDelete - 循环盘点删除
87. ntrCCDetailAdd - 循环盘点明细添加
88. ntrCCDetailUpd - 循环盘点明细更新
89. ntrCCDetailDel - 循环盘点明细删除

### 任务相关触发器
90. ntrTaskDetailAdd - 任务明细添加
91. ntrTaskDetailUpdate - 任务明细更新
92. ntrTaskDetailDelete - 任务明细删除

### 波次相关触发器
93. ntrWaveHeaderDelete - 波次头删除

### 投放ID触发器
94. ntrDropIDHeaderAdd - 投放ID头添加
95. ntrDropIDHeaderUpdate - 投放ID头更新
96. ntrDropIDHeaderDelete - 投放ID头删除
97. ntrDropIDDetailAdd - 投放ID明细添加
98. ntrDropIDDetailUpdate - 投放ID明细更新
99. ntrDropIDDetailDelete - 投放ID明细删除

### 区域管理触发器
100. ntrAreaDetailDelete - 区域明细删除
101. ntrAreaDetailUpdate - 区域明细更新
102. ntrPutawayZoneDelete - 上架区域删除
103. ntrPutawayZoneUpdate - 上架区域更新
104. ntrSectionDelete - 区段删除
105. ntrSectionUpdate - 区段更新

### 批次ID明细触发器
106. ntrLotxIDDetailAdd - 批次ID明细添加
107. ntrLotxIDDetailUpdate - 批次ID明细更新
108. ntrLotxIDDetailDelete - 批次ID明细删除

## 总结
- **存储过程总数**: 167个
- **触发器总数**: 108个
- **总计**: 275个数据库对象

这些存储过程和触发器涵盖了完整的仓库管理系统功能，包括：
- 库存管理
- 订单处理
- 收发货管理
- 拣货和包装
- 运输管理
- 计费系统
- 任务管理
- 报表分析
- 数据归档
- 系统维护

所有对象都已成功分离到对应的文件中，便于后续的维护和管理。
